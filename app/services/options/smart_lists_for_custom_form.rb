class Options::SmartListsForCustomForm < ApplicationService
  def initialize(current_company, params)
    @current_company = current_company
    @params = params
    @total_count = 0
  end

  def call
    options_ids = fetch_options
    linkables = fetch_linkables

    my_options = []
    model_class = @params[:name].constantize
    list_options = model_class.where(id: options_ids)
  
    if model_class.name == 'Contributor'
      list_options = list_options.includes(:company_user, :group, :guest)
  
      my_options << list_options.map do |contributor|
        {
          id: contributor.id,
          name: contributor.group&.name ||
                contributor.company_user&.full_name ||
                contributor.guest&.full_name ||
                contributor.guest&.email
        }
      end
    else
      my_options << list_options.map { |option| { id: option.id, name: option.name } }
    end
  
    unless @params[:name] == 'Contributor'
      related_item_options = linkables.select { |item| item.linkable_type == @params[:name] }

      # Batch load the actual entities to get their names without N+1
      if related_item_options.any?
        entity_class = @params[:name].constantize
        entity_ids = related_item_options.map(&:linkable_id)
        entities = entity_class.where(id: entity_ids).index_by(&:id)

        my_options << related_item_options.map do |opt|
          entity = entities[opt.linkable_id]
          { id: opt.linkable_id, name: entity&.name || opt.name }
        end
      end
    end
    my_options = my_options.flatten.uniq
    my_options = my_options[offset, limit] if offset.present? && limit.present?
    my_options_count = @total_count

    { options: my_options, count: my_options_count }
  end

  private
    def offset
      @params[:offset].to_i if @params[:offset]
    end

    def limit
      @params[:limit].to_i if @params[:limit]
    end

    def fetch_options      
      options_query = @current_company.custom_form_values.joins(:custom_form_field)
                                                         .where(custom_form_fields: { field_attribute_type: @params[:field_type] })
                                                         .pluck(:value_int)
                                                         .uniq
      @total_count = options_query.length
      options_query
    end

    def fetch_linkables
      linkable_links = LinkableLink.joins(:source)
                                   .includes(:target)
                                   .where(linkables: { linkable_type: "HelpTicket", company_id: @current_company.id })
      linkables_query = linkable_links.map(&:target)
      if (@params[:name] != "ManagedAsset" && @total_count == 0)
        @total_count = linkables_query.select { |item| item.linkable_type == @params[:name]}.length
      end
      linkables_query
    end
end
