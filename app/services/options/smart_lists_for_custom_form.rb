class Options::SmartListsForCustomForm < ApplicationService
  def initialize(current_company, params)
    @current_company = current_company
    @params = params
    @total_count = 0
  end

  def call
    options_ids = fetch_options
    linkables = fetch_linkables

    my_options = []

    list_options = @params[:name].constantize.where(id: options_ids)
    my_options << list_options.map { |option| { id: option.id, name: option.name } }
    if @params[:name] == 'Contributor'
      related_item_options = linkables.select{ |item| ( item.linkable_type == 'CompanyUser' || item.linkable_type == 'Group' )}
      my_options << build_contributor_options(related_item_options)
    else
      related_item_options = linkables.select{ |item| item.linkable_type == @params[:name] }
      my_options << related_item_options.map{ |opt| { id: opt.linkable_id, name: opt.name } }
    end
    my_options = my_options.flatten.uniq
    my_options = my_options[offset, limit] if offset.present? && limit.present?
    my_options_count = @total_count

    { options: my_options, count: my_options_count }
  end

  private
    def offset
      @params[:offset].to_i if @params[:offset]
    end

    def limit
      @params[:limit].to_i if @params[:limit]
    end

    def build_contributor_options(related_item_options)
      # OPTIMIZE: Batch load CompanyUser and Group records to eliminate N+1 queries
      company_user_ids = []
      group_ids = []

      # Separate the IDs by type to batch load efficiently
      related_item_options.each do |opt|
        case opt.linkable_type
        when 'CompanyUser'
          company_user_ids << opt.linkable_id
        when 'Group'
          group_ids << opt.linkable_id
        end
      end

      # Batch load all CompanyUsers and Groups with their contributors in 2 queries instead of N queries
      company_users = CompanyUser.includes(:contributor).where(id: company_user_ids).index_by(&:id)
      groups = Group.includes(:contributor).where(id: group_ids).index_by(&:id)

      # Build options efficiently using the pre-loaded data (O(1) hash lookups)
      related_item_options.map do |opt|
        contributor_id = case opt.linkable_type
                         when 'CompanyUser'
                          company_users[opt.linkable_id]&.contributor&.id
                         when 'Group'
                          groups[opt.linkable_id]&.contributor&.id
                         end

        { id: contributor_id, name: opt.name } if contributor_id
      end.compact
    end

    def fetch_options      
      options_query = @current_company.custom_form_values.joins(:custom_form_field)
                                                         .where(custom_form_fields: { field_attribute_type: @params[:field_type] })
                                                         .pluck(:value_int)
                                                         .uniq
      @total_count = options_query.length
      options_query
    end

    def fetch_linkables
      linkables_query = LinkableLink.joins(:source)
                                    .where(linkables: { linkable_type: "HelpTicket", company_id: @current_company.id })
                                    .map{ |item| item.target }
      if (@params[:name] != "ManagedAsset" && @total_count == 0)
        @total_count = linkables_query.select { |item| item.linkable_type == @params[:name]}.length
      end
      linkables_query
    end
end
