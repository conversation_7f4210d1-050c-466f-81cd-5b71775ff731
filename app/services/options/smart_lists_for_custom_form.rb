class Options::SmartListsForCustomForm < ApplicationService
  def initialize(current_company, params)
    @current_company = current_company
    @params = params
    @total_count = 0
  end

  def call
    options_ids = fetch_options
    linkables = fetch_linkables

    my_options = []

    list_options = @params[:name].constantize.where(id: options_ids)
    my_options << list_options.map { |option| { id: option.id, name: option.name } }
    if @params[:name] == 'Contributor'
      related_item_options = linkables.select do |item|
        %w[CompanyUser Group].include?(item.linkable_type)
      end
      my_options << build_contributor_options(related_item_options)
    else
      related_item_options = linkables.select { |item| item.linkable_type == @params[:name] }
      my_options << related_item_options.map do |opt|
        { id: opt.linkable_id, name: opt.name }
      end
    end
    my_options = my_options.flatten.uniq
    my_options = my_options[offset, limit] if offset.present? && limit.present?
    my_options_count = @total_count

    { options: my_options, count: my_options_count }
  end

  private
    def offset
      @params[:offset].to_i if @params[:offset]
    end

    def limit
      @params[:limit].to_i if @params[:limit]
    end

    def build_contributor_options(related_item_options)
      # Collect IDs by type to batch load
      company_user_ids = []
      group_ids = []

      related_item_options.each do |opt|
        case opt.linkable_type
        when 'CompanyUser'
          company_user_ids << opt.linkable_id
        when 'Group'
          group_ids << opt.linkable_id
        end
      end

      # Batch load with contributors to eliminate N+1
      company_users = CompanyUser.includes(:contributor).where(id: company_user_ids).index_by(&:id)
      groups = Group.includes(:contributor).where(id: group_ids).index_by(&:id)

      # Build options using pre-loaded data
      related_item_options.map do |opt|
        case opt.linkable_type
        when 'CompanyUser'
          contributor = company_users[opt.linkable_id]&.contributor
        when 'Group'
          contributor = groups[opt.linkable_id]&.contributor
        end

        { id: contributor.id, name: opt.name } if contributor.present?
      end.compact
    end

    def fetch_options      
      options_query = @current_company.custom_form_values.joins(:custom_form_field)
                                                         .where(custom_form_fields: { field_attribute_type: @params[:field_type] })
                                                         .pluck(:value_int)
                                                         .uniq
      @total_count = options_query.length
      options_query
    end

    def fetch_linkables
      linkables_query = LinkableLink.joins(:source)
                                    .includes(:target)
                                    .where(linkables: { linkable_type: "HelpTicket", company_id: @current_company.id })
                                    .map(&:target)
    
      if (@params[:name] != "ManagedAsset" && @total_count == 0)
        @total_count = linkables_query.select { |item| item.linkable_type == @params[:name] }.length
      end
      linkables_query
    end
end
