class Options::SmartListsForCustomForm < ApplicationService
  def initialize(current_company, params)
    @current_company = current_company
    @params = params
    @total_count = 0
  end

  def call
    options_ids = fetch_options
    linkables = fetch_linkables

    my_options = []

    list_options = @params[:name].constantize.where(id: options_ids)
    my_options << list_options.map { |option| { id: option.id, name: option.name } }
    if @params[:name] == 'Contributor'
      related_item_options = linkables.select{ |item| ( item.linkable_type == 'CompanyUser' || item.linkable_type == 'Group' || item.linkable_type == 'Guest' )}
      my_options << build_contributor_options(related_item_options)
    else
      related_item_options = linkables.select{ |item| item.linkable_type == @params[:name] }
      my_options << related_item_options.map{ |opt| { id: opt.linkable_id, name: opt.name } }
    end
    my_options = my_options.flatten.uniq
    my_options = my_options[offset, limit] if offset.present? && limit.present?
    my_options_count = @total_count

    { options: my_options, count: my_options_count }
  end

  private
    def offset
      @params[:offset].to_i if @params[:offset]
    end

    def limit
      @params[:limit].to_i if @params[:limit]
    end

    def build_contributor_options(related_item_options)
      # OPTIMIZE: Use the already loaded linkable targets to avoid additional queries
      # Since fetch_linkables already loaded all associations, we can use them directly
      related_item_options.filter_map do |opt|
        # Access the pre-loaded contributor directly from the linkable target
        contributor_id = case opt.linkable_type
                         when 'CompanyUser'
                           opt.company_user&.contributor&.id
                         when 'Group'
                           opt.group&.contributor&.id
                         when 'Guest'
                           opt.guest&.contributor&.id
                         end

        { id: contributor_id, name: opt.name } if contributor_id
      end
    end

    def fetch_options      
      options_query = @current_company.custom_form_values.joins(:custom_form_field)
                                                         .where(custom_form_fields: { field_attribute_type: @params[:field_type] })
                                                         .pluck(:value_int)
                                                         .uniq
      @total_count = options_query.length
      options_query
    end

    def fetch_linkables
      # OPTIMIZE: Use includes to eager load target associations and avoid N+1 queries
      # OPTIMIZE: Include nested associations to prevent User#find, Guest#find, and Group#find N+1 queries
      linkable_links = LinkableLink.joins(:source)
                                   .includes(target: {
                                     company_user: [:user, :contributor],
                                     group: [:contributor],
                                     guest: [:contributor]
                                   })
                                   .where(linkables: { linkable_type: "HelpTicket", company_id: @current_company.id })

      # Extract targets efficiently without triggering additional queries
      linkables_query = linkable_links.map(&:target)

      # OPTIMIZE: Use database count when possible for better performance
      if (@params[:name] != "ManagedAsset" && @total_count == 0)
        if @params[:name].present?
          # Try to count at database level first
          @total_count = linkable_links.joins(:target)
                                      .where(target: { linkable_type: @params[:name] })
                                      .count
        else
          @total_count = linkables_query.count { |item| item.linkable_type == @params[:name] }
        end
      end
      linkables_query
    end
end
