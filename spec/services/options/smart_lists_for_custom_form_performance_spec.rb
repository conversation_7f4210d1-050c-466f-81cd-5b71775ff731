require 'rails_helper'

RSpec.describe Options::SmartListsForCustomForm, type: :service do
  include CompanyUserHelper
  include HelpTicketHelper

  create_company_and_user

  let(:custom_form) { create(:custom_form, company: company, form_name: 'Test Form', company_module: 'helpdesk') }
  let(:help_ticket) { create(:help_ticket, custom_form: custom_form, company: company) }

  describe 'N+1 Query Optimization' do
    context 'with ManagedAsset field type' do
      let!(:asset_list_field) { create(:custom_form_field, custom_form: custom_form, field_attribute_type: 'asset_list') }
      let!(:assets) { create_list(:managed_asset, 10, company: company) }
      let!(:asset_values) do
        assets.map do |asset|
          create(:custom_form_value, 
                 custom_form_field: asset_list_field, 
                 value_int: asset.id, 
                 custom_form: custom_form, 
                 module: help_ticket, 
                 company: company, 
                 module_type: 'HelpTicket', 
                 module_id: help_ticket.id)
        end
      end

      it 'executes minimal database queries' do
        service = Options::SmartListsForCustomForm.new(company, {
          name: 'ManagedAsset',
          field_type: 'asset_list',
          limit: 20,
          offset: 0
        })

        # Count queries executed during service call
        query_count = 0
        callback = lambda do |name, started, finished, unique_id, payload|
          query_count += 1 if payload[:sql] && !payload[:name]&.include?('SCHEMA')
        end

        ActiveSupport::Notifications.subscribed(callback, 'sql.active_record') do
          result = service.call
          
          expect(result[:options]).to be_present
          expect(result[:count]).to eq(10)
          expect(result[:options].size).to eq(10)
        end

        # Should execute significantly fewer queries than before optimization
        # Before: ~12+ queries (1 for options + 1 for linkables + 10 for each target)
        # After: ~4 queries (1 for options + 1 for linkables with includes + 1 for assets + 1 for count)
        expect(query_count).to be <= 6
      end
    end

    context 'with Contributor field type' do
      let!(:contributor_list_field) { create(:custom_form_field, custom_form: custom_form, field_attribute_type: 'contributor_list') }
      let!(:company_users) { create_list(:company_user, 5, company: company) }
      let!(:groups) { create_list(:group, 3, company: company) }
      
      before do
        # Create linkable links for company users and groups
        (company_users + groups).each do |entity|
          create(:linkable_link, 
                 source: help_ticket.linkable, 
                 target: entity.linkable)
        end
      end

      it 'optimizes contributor access without N+1 queries' do
        service = Options::SmartListsForCustomForm.new(company, {
          name: 'Contributor',
          field_type: 'contributor_list',
          limit: 20,
          offset: 0
        })

        query_count = 0
        callback = lambda do |name, started, finished, unique_id, payload|
          query_count += 1 if payload[:sql] && !payload[:name]&.include?('SCHEMA')
        end

        ActiveSupport::Notifications.subscribed(callback, 'sql.active_record') do
          result = service.call
          
          expect(result[:options]).to be_present
          expect(result[:count]).to eq(8) # 5 company users + 3 groups
          expect(result[:options].all? { |opt| opt[:id].present? && opt[:name].present? }).to be true
        end

        # Should execute minimal queries:
        # 1 for options, 1 for linkables with includes, 1 for company_users with contributors, 1 for groups with contributors
        expect(query_count).to be <= 8
      end
    end

    context 'with large dataset' do
      let!(:asset_list_field) { create(:custom_form_field, custom_form: custom_form, field_attribute_type: 'asset_list') }
      let!(:assets) { create_list(:managed_asset, 50, company: company) }
      let!(:help_tickets) { create_list(:help_ticket, 20, custom_form: custom_form, company: company) }
      
      before do
        # Create many linkable relationships
        help_tickets.each do |ticket|
          assets.sample(5).each do |asset|
            create(:linkable_link, source: ticket.linkable, target: asset.linkable)
          end
        end

        # Create custom form values
        assets.each do |asset|
          create(:custom_form_value,
                 custom_form_field: asset_list_field,
                 value_int: asset.id,
                 custom_form: custom_form,
                 module: help_tickets.sample,
                 company: company,
                 module_type: 'HelpTicket')
        end
      end

      it 'scales efficiently with large datasets' do
        service = Options::SmartListsForCustomForm.new(company, {
          name: 'ManagedAsset',
          field_type: 'asset_list',
          limit: 20,
          offset: 0
        })

        start_time = Time.current
        query_count = 0
        
        callback = lambda do |name, started, finished, unique_id, payload|
          query_count += 1 if payload[:sql] && !payload[:name]&.include?('SCHEMA')
        end

        ActiveSupport::Notifications.subscribed(callback, 'sql.active_record') do
          result = service.call
          expect(result[:options]).to be_present
        end

        execution_time = Time.current - start_time
        
        # Should complete quickly even with large dataset
        expect(execution_time).to be < 1.0 # Less than 1 second
        expect(query_count).to be <= 10 # Reasonable number of queries regardless of dataset size
      end
    end
  end

  describe 'Functional correctness' do
    it 'maintains backward compatibility with existing behavior' do
      # Test that the optimized service produces the same results as before
      asset_list_field = create(:custom_form_field, custom_form: custom_form, field_attribute_type: 'asset_list')
      assets = create_list(:managed_asset, 3, company: company)
      
      assets.each do |asset|
        create(:custom_form_value,
               custom_form_field: asset_list_field,
               value_int: asset.id,
               custom_form: custom_form,
               module: help_ticket,
               company: company,
               module_type: 'HelpTicket',
               module_id: help_ticket.id)
      end

      service = Options::SmartListsForCustomForm.new(company, {
        name: 'ManagedAsset',
        field_type: 'asset_list',
        limit: 20,
        offset: 0
      })

      result = service.call
      
      expect(result[:options].size).to eq(3)
      expect(result[:count]).to eq(3)
      expect(result[:options].map { |opt| opt[:id] }).to match_array(assets.map(&:id))
      expect(result[:options].map { |opt| opt[:name] }).to match_array(assets.map(&:name))
    end
  end
end
