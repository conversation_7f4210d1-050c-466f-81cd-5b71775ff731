require 'rails_helper'

include CompanyUserHelper
include HelpTicketHelper

RSpec.describe Options::SmartListsForCustomFormsController, type: :controller do
  create_company_and_user

  describe 'GET #index' do
    let(:custom_form_1) {create(:custom_form, company: company, form_name: 'Blank State Copy3', company_module: 'helpdesk') }
    let!(:created_asset_value) { create(:custom_form_value, custom_form_field: asset_list_field, value_int: asset1.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:created_asset_value_1) { create(:custom_form_value, custom_form_field: asset_list_field, value_int: asset2.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:created_asset_value_2) { create(:custom_form_value, custom_form_field: asset_list_field, value_int: asset3.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:created_by_ticket) { create(:help_ticket, custom_form: custom_form_1, company: company) }
    let!(:asset_list_field) { create(:custom_form_field, custom_form_id: custom_form_1.id, label: 'Asset', name: 'asset', field_attribute_type: 'asset_list')}

    let!(:asset1) { create(:managed_asset, name: 'Asset1', company: company) }
    let!(:asset2) { create(:managed_asset, name: 'Asset2', company: company) }
    let!(:asset3) { create(:managed_asset, name: 'Asset3', company: company) }

    let!(:created_vendor_value_3) { create(:custom_form_value, custom_form_field: vendor_list_field, value_int: vendor1.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:created_vendor_value_4) { create(:custom_form_value, custom_form_field: vendor_list_field, value_int: vendor2.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:created_vendor_value_5) { create(:custom_form_value, custom_form_field: vendor_list_field, value_int: vendor3.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:vendor_list_field) { create(:custom_form_field, custom_form_id: custom_form_1.id, label: 'Vendor', name: 'vendor', field_attribute_type: 'vendor_list')}

    let!(:vendor1) { create(:vendor, name: 'vendor1', company: company) }
    let!(:vendor2) { create(:vendor, name: 'vendor2', company: company) }
    let!(:vendor3) { create(:vendor, name: 'vendor3', company: company) }

    let!(:created_contract_value_6) { create(:custom_form_value, custom_form_field: contract_list_field, value_int: contract1.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:created_contract_value_7) { create(:custom_form_value, custom_form_field: contract_list_field, value_int: contract2.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:created_contract_value_8) { create(:custom_form_value, custom_form_field: contract_list_field, value_int: contract3.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:contract_list_field) { create(:custom_form_field, custom_form_id: custom_form_1.id, label: 'Contract', name: 'contract', field_attribute_type: 'contract_list')}

    let!(:contract1) { create(:contract, name: 'contract1', company: company) }
    let!(:contract2) { create(:contract, name: 'contract2', company: company) }
    let!(:contract3) { create(:contract, name: 'contract3', company: company) }

    let!(:created_location_value_12) { create(:custom_form_value, custom_form_field: location_list_field, value_int: location1.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:created_location_value_13) { create(:custom_form_value, custom_form_field: location_list_field, value_int: location2.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:created_location_value_14) { create(:custom_form_value, custom_form_field: location_list_field, value_int: location3.id, custom_form: custom_form_1, module: created_by_ticket, company: company, module_type: 'HelpTicket', module_id: created_by_ticket.id) }
    let!(:location_list_field) { create(:custom_form_field, custom_form_id: custom_form_1.id, label: 'Location', name: 'location', field_attribute_type: 'location_list')}

    let!(:location1) { create(:location,:phone_number,company: company,phone_number_country_code_number: "44") }
    let!(:location2) { create(:location,:phone_number,company: company,phone_number_country_code_number: "92") }
    let!(:location3) { create(:location,:phone_number,company: company,phone_number_country_code_number: "91") }

    it 'returns a list of Assets smart lists for custom forms' do
      get :index, params: { limit: 20, offset: 0, name: 'ManagedAsset', field_type: 'asset_list' }

      options = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response.body).to be_present

      expect(options).to be_present
      expect(options["count"]).to eq(3)
      expect(options["options"].pluck("id")).to match_array([asset1.id, asset2.id, asset3.id])
    end

    it 'returns a list of Vendor smart lists for custom forms' do
      get :index, params: { limit: 20, offset: 0, name: 'Vendor', field_type: 'vendor_list' }

      options = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response.body).to be_present

      expect(options).to be_present
      expect(options["count"]).to eq(3)
      expect(options["options"].pluck("id")).to match_array([vendor1.id, vendor2.id, vendor3.id])
    end

    it 'returns a list of Contract smart lists for custom forms' do
      get :index, params: { limit: 20, offset: 0, name: 'Contract', field_type: 'contract_list' }

      options = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response.body).to be_present

      expect(options).to be_present
      expect(options["count"]).to eq(3)
      expect(options["options"].pluck("id")).to match_array([contract1.id, contract2.id, contract3.id])
    end

    it 'returns a list of location smart lists for custom forms' do
      get :index, params: { limit: 20, offset: 0, name: 'Location', field_type: 'location_list' }

      options = JSON.parse(response.body)
      expect(response).to have_http_status(:ok)
      expect(response.body).to be_present

      expect(options).to be_present
      expect(options["count"]).to eq(3)
      expect(options["options"].pluck("id")).to match_array([location1.id, location2.id, location3.id])
    end
  end

  describe 'N+1 Query Performance Optimization' do
    let(:custom_form) { create(:custom_form, company: company, form_name: 'Performance Test Form', company_module: 'helpdesk') }
    let(:help_ticket) { create(:help_ticket, custom_form: custom_form, company: company) }

    context 'with optimized queries' do
      let!(:asset_list_field) { create(:custom_form_field, custom_form: custom_form, label: 'Asset List', field_attribute_type: 'asset_list') }
      let!(:assets) { create_list(:managed_asset, 10, company: company) }
      let!(:asset_values) do
        assets.map do |asset|
          create(:custom_form_value,
                 custom_form_field: asset_list_field,
                 value_int: asset.id,
                 custom_form: custom_form,
                 module: help_ticket,
                 company: company,
                 module_type: 'HelpTicket',
                 module_id: help_ticket.id)
        end
      end

      it 'executes minimal database queries for asset lists' do
        query_count = 0
        callback = lambda do |name, started, finished, unique_id, payload|
          query_count += 1 if payload[:sql] && !payload[:name]&.include?('SCHEMA')
        end

        ActiveSupport::Notifications.subscribed(callback, 'sql.active_record') do
          get :index, params: { limit: 20, offset: 0, name: 'ManagedAsset', field_type: 'asset_list' }

          options = JSON.parse(response.body)
          expect(response).to have_http_status(:ok)
          expect(options["options"]).to be_present
          expect(options["count"]).to eq(10)
        end

        # Should execute significantly fewer queries than before optimization
        # Before: ~12+ queries (1 for options + 1 for linkables + 10 for each target)
        # After: ~6 queries (optimized with includes)
        expect(query_count).to be <= 8
      end
    end

    context 'with people list field type' do
      let!(:people_list_field) { create(:custom_form_field, custom_form: custom_form, label: 'People List', field_attribute_type: 'people_list') }
      let!(:company_users) { create_list(:company_user, 3, company: company) }
      let!(:groups) { create_list(:group, 2, company: company) }

      before do
        # Create linkable links for company users and groups
        (company_users + groups).each do |entity|
          create(:linkable_link,
                 source: help_ticket.linkable,
                 target: entity.linkable)
        end
      end

      it 'optimizes contributor access without N+1 queries' do
        query_count = 0
        callback = lambda do |name, started, finished, unique_id, payload|
          query_count += 1 if payload[:sql] && !payload[:name]&.include?('SCHEMA')
        end

        ActiveSupport::Notifications.subscribed(callback, 'sql.active_record') do
          get :index, params: { limit: 20, offset: 0, name: 'Contributor', field_type: 'people_list' }

          options = JSON.parse(response.body)
          expect(response).to have_http_status(:ok)
          expect(options["options"]).to be_present
        end

        # Should execute minimal queries with batch loading
        expect(query_count).to be <= 10
      end
    end
  end
end
