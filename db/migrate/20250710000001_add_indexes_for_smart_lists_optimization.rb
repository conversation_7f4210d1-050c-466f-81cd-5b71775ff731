class AddIndexesForSmartListsOptimization < ActiveRecord::Migration[7.0]
  def change
    # Add composite index for custom_form_values to optimize fetch_options query
    # This supports the query: custom_form_values.joins(:custom_form_field).where(custom_form_fields: { field_attribute_type: ? })
    add_index :custom_form_values, [:custom_form_field_id, :value_int], 
              name: 'index_custom_form_values_on_field_id_and_value_int'
    
    # Add index for linkables to optimize target linkable queries
    # This supports filtering by linkable_type in the optimized fetch_linkables method
    add_index :linkables, [:linkable_type, :company_id], 
              name: 'index_linkables_on_type_and_company_id'
  end
end
